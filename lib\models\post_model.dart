import 'package:cloud_firestore/cloud_firestore.dart';

class PostModel {
  final String id;
  final String userId;
  final String username;
  final String userDisplayName;
  final String? userProfileImageUrl;
  final String content;
  final List<String> imageUrls;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> likes;
  final List<String> comments;
  final List<String> shares;
  final bool isActive;
  final DocumentSnapshot? lastDocument; // For pagination

  PostModel({
    required this.id,
    required this.userId,
    required this.username,
    required this.userDisplayName,
    this.userProfileImageUrl,
    required this.content,
    this.imageUrls = const [],
    required this.createdAt,
    required this.updatedAt,
    this.likes = const [],
    this.comments = const [],
    this.shares = const [],
    this.isActive = true,
    this.lastDocument,
  });

  // Convert PostModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'username': username,
      'userDisplayName': userDisplayName,
      'userProfileImageUrl': userProfileImageUrl,
      'content': content,
      'imageUrls': imageUrls,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'isActive': isActive,
    };
  }

  // Create PostModel from Firestore document
  factory PostModel.fromMap(Map<String, dynamic> map) {
    return PostModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      username: map['username'] ?? '',
      userDisplayName: map['userDisplayName'] ?? '',
      userProfileImageUrl: map['userProfileImageUrl'],
      content: map['content'] ?? '',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: List<String>.from(map['likes'] ?? []),
      comments: List<String>.from(map['comments'] ?? []),
      shares: List<String>.from(map['shares'] ?? []),
      isActive: map['isActive'] ?? true,
    );
  }

  // Create PostModel from Firestore DocumentSnapshot
  factory PostModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PostModel(
      id: data['id'] ?? '',
      userId: data['userId'] ?? '',
      username: data['username'] ?? '',
      userDisplayName: data['userDisplayName'] ?? '',
      userProfileImageUrl: data['userProfileImageUrl'],
      content: data['content'] ?? '',
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: List<String>.from(data['likes'] ?? []),
      comments: List<String>.from(data['comments'] ?? []),
      shares: List<String>.from(data['shares'] ?? []),
      isActive: data['isActive'] ?? true,
      lastDocument: doc, // Store the document for pagination
    );
  }

  // Create a copy of PostModel with updated fields
  PostModel copyWith({
    String? id,
    String? userId,
    String? username,
    String? userDisplayName,
    String? userProfileImageUrl,
    String? content,
    List<String>? imageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? likes,
    List<String>? comments,
    List<String>? shares,
    bool? isActive,
  }) {
    return PostModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userDisplayName: userDisplayName ?? this.userDisplayName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      content: content ?? this.content,
      imageUrls: imageUrls ?? this.imageUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      isActive: isActive ?? this.isActive,
    );
  }

  // Get like count
  int get likeCount => likes.length;

  // Get comment count
  int get commentCount => comments.length;

  // Get share count
  int get shareCount => shares.length;

  // Check if post is liked by user
  bool isLikedBy(String userId) => likes.contains(userId);

  // Check if post has images
  bool get hasImages => imageUrls.isNotEmpty;

  // Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'PostModel(id: $id, userId: $userId, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PostModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
