import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../services/post_service.dart';
import '../services/image_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/post_card.dart';
import 'comments_screen.dart';
import 'user_profile_screen.dart';

import 'dart:io' show File;
import 'dart:typed_data';

class FeedScreen extends StatefulWidget {
  const FeedScreen({super.key});

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  final TextEditingController _postController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<XFile> _selectedImages = [];
  bool _isCreatingPost = false;

  // Filter states
  String _selectedFilter = 'Global'; // Default filter

  // Pagination variables
  List<PostModel> _posts = [];
  bool _isLoadingMore = false;
  bool _hasMorePosts = true;
  DocumentSnapshot? _lastDocument;
  static const int _postsPerPage = 10;

  @override
  void initState() {
    super.initState();
    _testFirestoreConnection();
    _setupScrollListener();
    // Load initial posts after a short delay to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMorePosts();
    });
  }

  void _testFirestoreConnection() async {
    final isConnected = await PostService.testFirestoreConnection();
    print('FeedScreen: Firestore connection test result: $isConnected');
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Load more posts when user is near the bottom
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 300) {
        _loadMorePosts();
      }
    });
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final newPosts = await PostService.getPostsPaginated(
        filter: _selectedFilter,
        currentUserId: Provider.of<AuthProvider>(context, listen: false).currentUser?.id,
        lastDocument: _lastDocument,
        limit: _postsPerPage,
      );

      if (newPosts.isNotEmpty) {
        setState(() {
          _posts.addAll(newPosts);
          if (newPosts.isNotEmpty) {
            _lastDocument = newPosts.last.lastDocument;
          }
          _hasMorePosts = newPosts.length == _postsPerPage;
        });
      } else {
        setState(() {
          _hasMorePosts = false;
        });
      }
    } catch (e) {
      print('Error loading more posts: $e');
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _refreshPosts() {
    setState(() {
      _posts.clear();
      _lastDocument = null;
      _hasMorePosts = true;
    });
  }

  @override
  void dispose() {
    _postController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Image.asset(
          'assets/icons/amar_logo.png',
          height: 40,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to app name if logo fails to load
            return Text(
              AppConstants.appName,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            );
          },
        ),
        centerTitle: false,
        automaticallyImplyLeading: false, // This removes the back button
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Scrollable Content with Post Creation and Sticky Filter
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                _refreshPosts();
                await _loadMorePosts();
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                // Post Creation Section (Scrollable)
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: const BoxDecoration(
                      color: AppConstants.surfaceColor,
                      border: Border(
                        bottom: BorderSide(
                          color: AppConstants.backgroundColor,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                final user = authProvider.currentUser;
                                return CircleAvatar(
                                  radius: 20,
                                  backgroundImage: user?.profileImageUrl != null
                                      ? NetworkImage(user!.profileImageUrl!)
                                      : null,
                                  child: user?.profileImageUrl == null
                                      ? Text(
                                          user?.displayName?.isNotEmpty == true
                                              ? user!.displayName![0].toUpperCase()
                                              : user?.email?.isNotEmpty == true
                                                  ? user!.email![0].toUpperCase()
                                                  : 'U',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        )
                                      : null,
                                );
                              },
                            ),
                            const SizedBox(width: AppConstants.paddingSmall),
                            Expanded(
                              child: TextField(
                                controller: _postController,
                                maxLines: null,
                                decoration: const InputDecoration(
                                  hintText: 'What\'s on your mind?',
                                  border: InputBorder.none,
                                  hintStyle: TextStyle(color: Colors.grey),
                                ),
                              ),
                            ),
                          ],
                        ),

                        // Image Preview Section
                        if (_selectedImages.isNotEmpty) ...[
                          const SizedBox(height: AppConstants.paddingMedium),
                          _buildImagePreview(),
                        ],

                        const SizedBox(height: AppConstants.paddingMedium),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                TextButton.icon(
                                  onPressed: _isCreatingPost ? null : _pickImagesFromGallery,
                                  icon: const Icon(Icons.photo_library_outlined),
                                  label: const Text('Gallery'),
                                ),
                                TextButton.icon(
                                  onPressed: _isCreatingPost ? null : _pickImageFromCamera,
                                  icon: const Icon(Icons.camera_alt_outlined),
                                  label: const Text('Camera'),
                                ),
                              ],
                            ),
                            ElevatedButton(
                              onPressed: _isCreatingPost ? null : _createPost,
                              child: _isCreatingPost
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Text('Post'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Filter Section (Sticky)
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _FilterSectionDelegate(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade200),
                        ),
                      ),
                      child: Row(
                        children: [
                          _buildFilterButton('My Posts', 'My Posts'),
                          const SizedBox(width: 8),
                          _buildFilterButton('Global', 'Global'),
                          const SizedBox(width: 8),
                          _buildFilterButton('Followed', 'Followed'),
                          const SizedBox(width: 8),
                          _buildFilterButton('Favorites', 'Favorites'),
                        ],
                      ),
                    ),
                  ),
                ),

                // Posts List (Scrollable with Pagination)
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      // Show initial loading
                      if (_posts.isEmpty && _isLoadingMore) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      // Show empty state
                      if (_posts.isEmpty && !_isLoadingMore) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _getEmptyStateIcon(),
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _getEmptyStateTitle(),
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _getEmptyStateSubtitle(),
                                  style: const TextStyle(color: Colors.grey),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () {
                                    _refreshPosts();
                                    _loadMorePosts();
                                  },
                                  child: const Text('Refresh'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      // Show post item
                      if (index < _posts.length) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: PostCard(
                            post: _posts[index],
                            currentUserId: Provider.of<AuthProvider>(context, listen: false).currentUser?.id,
                          ),
                        );
                      }

                      // Show loading indicator at the end
                      if (index == _posts.length && _hasMorePosts) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }

                      // End of list
                      return null;
                    },
                    childCount: _posts.isEmpty && !_isLoadingMore
                        ? 1 // Show empty state
                        : _posts.length + (_hasMorePosts ? 1 : 0), // Posts + loading indicator
                  ),
                ),
              ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Post creation and other methods





  // Helper Methods

  void _pickImagesFromGallery() async {
    try {
      final images = await ImageService.pickMultipleImages(maxImages: 5);
      if (images != null && images.isNotEmpty) {
        setState(() {
          // Add new images to existing selection
          _selectedImages.addAll(images);
          // Ensure we don't exceed maximum limit
          if (_selectedImages.length > 5) {
            _selectedImages = _selectedImages.take(5).toList();
          }
        });
        _showSuccessSnackBar('${images.length} image(s) selected');
      }
    } catch (e) {
      _showErrorSnackBar('Error selecting images: $e');
    }
  }

  void _pickImageFromCamera() async {
    try {
      final image = await ImageService.pickImageFromCamera();
      if (image != null) {
        setState(() {
          // Add camera image to existing selection
          _selectedImages.add(image);
          // Ensure we don't exceed maximum limit
          if (_selectedImages.length > 5) {
            _selectedImages = _selectedImages.take(5).toList();
          }
        });
        _showSuccessSnackBar('Photo captured successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error taking photo: $e');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }







  void _createPost() async {
    final content = _postController.text.trim();

    if (content.isEmpty && _selectedImages.isEmpty) {
      _showErrorSnackBar('Please add some content or images to your post');
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      _showErrorSnackBar('Please log in to create a post');
      return;
    }

    setState(() {
      _isCreatingPost = true;
    });

    try {
      print('FeedScreen: Starting post creation...');
      print('FeedScreen: Content length: ${content.length}');
      print('FeedScreen: Number of images: ${_selectedImages.length}');

      // Show progress message
      if (_selectedImages.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                const SizedBox(width: 16),
                Text('Uploading ${_selectedImages.length} image(s)...'),
              ],
            ),
            backgroundColor: Colors.blue,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 30),
          ),
        );
      }

      final post = await PostService.createPost(
        user: currentUser,
        content: content,
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
      );

      print('FeedScreen: Post creation result: ${post != null ? 'Success' : 'Failed'}');

      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (post != null) {
        _postController.clear();
        final imageCount = _selectedImages.length;
        setState(() {
          _selectedImages.clear();
        });

        final imageText = imageCount > 0
            ? ' with $imageCount image(s)'
            : '';
        _showSuccessSnackBar('Post created successfully$imageText! 🎉');

        // Refresh posts to show the new post
        _refreshPosts();
        _loadMorePosts();
      } else {
        _showErrorSnackBar('Failed to create post. Please try again.');
      }
    } catch (e) {
      print('Error creating post: $e');
      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _showErrorSnackBar('Error creating post: ${e.toString()}');
    } finally {
      setState(() {
        _isCreatingPost = false;
      });
    }
  }





  void _editPost(PostModel post) {
    showDialog(
      context: context,
      builder: (context) {
        final editController = TextEditingController(text: post.content);
        bool isUpdating = false;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Edit Post'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: editController,
                      maxLines: 5,
                      decoration: const InputDecoration(
                        hintText: 'What\'s on your mind?',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    if (post.hasImages) ...[
                      const SizedBox(height: 16),
                      const Text(
                        'Note: Image editing is not supported yet. Only text content can be edited.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isUpdating ? null : () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: isUpdating
                      ? null
                      : () async {
                          final newContent = editController.text.trim();
                          if (newContent.isEmpty && !post.hasImages) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Post content cannot be empty'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          setState(() {
                            isUpdating = true;
                          });

                          try {
                            final updatedPost = post.copyWith(
                              content: newContent,
                              updatedAt: DateTime.now(),
                            );

                            final success = await PostService.updatePost(updatedPost);

                            if (success) {
                              Navigator.pop(context);
                              _showSuccessSnackBar('Post updated successfully! 🎉');
                            } else {
                              _showErrorSnackBar('Failed to update post. Please try again.');
                            }
                          } catch (e) {
                            _showErrorSnackBar('Error updating post: $e');
                          } finally {
                            setState(() {
                              isUpdating = false;
                            });
                          }
                        },
                  child: isUpdating
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Update'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _deletePost(PostModel post) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Post'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Are you sure you want to delete this post?'),
              const SizedBox(height: 8),
              if (post.hasImages) ...[
                Text(
                  '• ${post.imageUrls.length} image(s) will also be deleted from cloud storage',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
              ],
              const Text(
                '• This action cannot be undone',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        // Show loading message
        final hasImages = post.hasImages;
        final loadingMessage = hasImages
            ? 'Deleting post and ${post.imageUrls.length} image(s)...'
            : 'Deleting post...';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                const SizedBox(width: 16),
                Text(loadingMessage),
              ],
            ),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 30),
          ),
        );

        final success = await PostService.deletePost(post.id);

        // Hide loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        if (success) {
          final successMessage = hasImages
              ? 'Post and ${post.imageUrls.length} image(s) deleted successfully! 🗑️'
              : 'Post deleted successfully! 🗑️';
          _showSuccessSnackBar(successMessage);
        } else {
          _showErrorSnackBar('Failed to delete post. Please try again.');
        }
      } catch (e) {
        // Hide loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showErrorSnackBar('Error deleting post: $e');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          final imageFile = _selectedImages[index];
          return Container(
            width: 100,
            margin: const EdgeInsets.only(right: 8),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: kIsWeb
                        ? FutureBuilder<Uint8List>(
                            future: imageFile.readAsBytes(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return Image.memory(
                                  snapshot.data!,
                                  fit: BoxFit.cover,
                                  width: 100,
                                  height: 100,
                                );
                              }
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Image.file(
                            File(imageFile.path),
                            fit: BoxFit.cover,
                            width: 100,
                            height: 100,
                          ),
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Build filter button
  Widget _buildFilterButton(String label, String filterValue) {
    final isSelected = _selectedFilter == filterValue;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (_selectedFilter != filterValue) {
            setState(() {
              _selectedFilter = filterValue;
            });
            print('FeedScreen: Filter changed to: $filterValue');
            _refreshPosts();
            _loadMorePosts();
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).primaryColor : Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey.shade700,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  // Get empty state icon based on current filter
  IconData _getEmptyStateIcon() {
    switch (_selectedFilter) {
      case 'My Posts':
        return Icons.person_outline;
      case 'Followed':
        return Icons.people_outline;
      case 'Favorites':
        return Icons.favorite_outline;
      case 'Global':
      default:
        return Icons.public_outlined;
    }
  }

  // Get empty state title based on current filter
  String _getEmptyStateTitle() {
    switch (_selectedFilter) {
      case 'My Posts':
        return 'No posts from you yet';
      case 'Followed':
        return 'No posts from followed users';
      case 'Favorites':
        return 'No favorite posts yet';
      case 'Global':
      default:
        return 'No posts yet';
    }
  }

  // Get empty state subtitle based on current filter
  String _getEmptyStateSubtitle() {
    switch (_selectedFilter) {
      case 'My Posts':
        return 'Share your first post to get started!';
      case 'Followed':
        return 'Follow some users to see their posts here';
      case 'Favorites':
        return 'Like some posts to see them in your favorites';
      case 'Global':
      default:
        return 'Be the first to share something!';
    }
  }


}

// Custom delegate for sticky filter section
class _FilterSectionDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _FilterSectionDelegate({required this.child});

  @override
  double get minExtent => 52.0; // Minimum height when collapsed

  @override
  double get maxExtent => 52.0; // Maximum height when expanded

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: maxExtent,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
