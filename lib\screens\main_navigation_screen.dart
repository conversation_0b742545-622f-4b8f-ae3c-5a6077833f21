import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import 'feed_screen.dart';
import 'search_screen.dart';
import 'market_screen.dart';
import 'chat_screen.dart';
import 'profile_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 2; // Default to Market tab

  final List<Widget> _screens = [
    const FeedScreen(),
    const SearchScreen(),
    const MarketScreen(),
    const ChatScreen(),
    const ProfileScreen(),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        selectedItemColor: AppConstants.primaryColor,
        unselectedItemColor: AppConstants.textSecondaryColor,
        backgroundColor: AppConstants.surfaceColor,
        elevation: 8,
        selectedLabelStyle: const TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w400,
        ),
        items: List.generate(
          AppConstants.bottomNavLabels.length,
          (index) => BottomNavigationBarItem(
            icon: Icon(
              AppConstants.bottomNavIcons[index],
              size: AppConstants.iconSizeMedium,
            ),
            activeIcon: Icon(
              AppConstants.bottomNavIcons[index],
              size: AppConstants.iconSizeMedium,
              color: AppConstants.primaryColor,
            ),
            label: AppConstants.bottomNavLabels[index],
          ),
        ),
      ),
    );
  }
}
