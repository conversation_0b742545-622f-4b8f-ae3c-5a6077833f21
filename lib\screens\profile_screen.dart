import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../services/post_service.dart';
import '../services/product_service.dart';
import '../services/image_service.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        centerTitle: true,
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const EditProfileScreen(),
                ),
              );
            },
            tooltip: 'Edit Profile',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'logout') {
                _handleLogout();
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: AppConstants.errorColor),
                    SizedBox(width: AppConstants.paddingSmall),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          // Cover Photo and Profile Header
          SliverToBoxAdapter(
            child: Column(
              children: [
                // Cover Photo Section
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final user = authProvider.currentUser;
                    return Container(
                  height: 180,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppConstants.backgroundColor,
                    image: user?.coverImageUrl != null
                        ? DecorationImage(
                            image: CachedNetworkImageProvider(user!.coverImageUrl!),
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                  child: Stack(
                    children: [
                      // Gradient overlay for better text visibility
                      if (user?.coverImageUrl != null)
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.3),
                              ],
                            ),
                          ),
                        ),
                      // Default cover when no image
                      if (user?.coverImageUrl == null)
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppConstants.primaryColor.withOpacity(0.8),
                                AppConstants.primaryColor.withOpacity(0.6),
                              ],
                            ),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.landscape,
                              size: 64,
                              color: AppConstants.onPrimaryColor,
                            ),
                          ),
                        ),
                      // Cover photo edit button
                      Positioned(
                        top: 16,
                        right: 16,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: () {
                              // TODO: Change cover photo
                              _showCoverPhotoOptions();
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            // Profile Header
            Container(
              padding: const EdgeInsets.fromLTRB(
                AppConstants.paddingLarge,
                AppConstants.paddingLarge,
                AppConstants.paddingLarge,
                AppConstants.paddingXLarge
              ),
              decoration: const BoxDecoration(
                color: AppConstants.surfaceColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppConstants.backgroundColor,
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                children: [
                  // Profile Section - Centered Layout like View Profile
                  Transform.translate(
                    offset: const Offset(0, -60),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Circular Profile Picture - Left Aligned
                          Stack(
                            children: [
                              Consumer<AuthProvider>(
                                builder: (context, authProvider, child) {
                                  final user = authProvider.currentUser;
                                  return GestureDetector(
                                    onTap: () => _viewProfilePhotoFullscreen(user?.profileImageUrl),
                                    child: Hero(
                                      tag: 'profile_photo_${user?.id ?? 'default'}',
                                      child: Container(
                                        width: 120,
                                        height: 120,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                            width: 3,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.1),
                                              blurRadius: 10,
                                              offset: const Offset(0, 5),
                                            ),
                                          ],
                                        ),
                                        child: ClipOval(
                                          child: user?.profileImageUrl != null
                                              ? CachedNetworkImage(
                                                  imageUrl: user!.profileImageUrl!,
                                                  fit: BoxFit.cover,
                                                  placeholder: (context, url) => Container(
                                                    color: AppConstants.backgroundColor,
                                                    child: const Center(
                                                      child: CircularProgressIndicator(),
                                                    ),
                                                  ),
                                                  errorWidget: (context, url, error) => Container(
                                                    color: AppConstants.primaryColor,
                                                    child: Center(
                                                      child: Text(
                                                        user.displayName.isNotEmpty
                                                            ? user.displayName[0].toUpperCase()
                                                            : 'U',
                                                        style: const TextStyle(
                                                          fontSize: 45,
                                                          fontWeight: FontWeight.bold,
                                                          color: AppConstants.onPrimaryColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Container(
                                                  color: AppConstants.primaryColor,
                                                  child: Center(
                                                    child: Text(
                                                      user?.displayName?.isNotEmpty == true
                                                          ? user!.displayName[0].toUpperCase()
                                                          : 'U',
                                                      style: const TextStyle(
                                                        fontSize: 45,
                                                        fontWeight: FontWeight.bold,
                                                        color: AppConstants.onPrimaryColor,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              // Profile Picture Edit Button
                              Positioned(
                                bottom: 5,
                                right: 5,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: AppConstants.primaryColor,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(15),
                                      onTap: _showProfilePhotoOptions,
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        child: const Icon(
                                          Icons.camera_alt,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: AppConstants.paddingMedium),

                          // User Info Below Profile Picture
                          Consumer<AuthProvider>(
                            builder: (context, authProvider, child) {
                              final user = authProvider.currentUser;
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Display Name with Verification Badge
                                  Row(
                                    children: [
                                      Flexible(
                                        child: Text(
                                          user?.displayName ?? 'User Name',
                                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: AppConstants.textPrimaryColor,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      if (user?.isVerified == true)
                                        const Padding(
                                          padding: EdgeInsets.only(left: 4),
                                          child: Icon(
                                            Icons.verified,
                                            color: Colors.blue,
                                            size: 20,
                                          ),
                                        ),
                                    ],
                                  ),

                                  const SizedBox(height: 8),

                                  // Username, Joining Date
                                  Row(
                                    children: [
                                      Text(
                                        '@${user?.username ?? 'username'}',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: AppConstants.textSecondaryColor,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '•',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: AppConstants.textSecondaryColor,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Joined ${_formatJoiningDate(user?.createdAt)}',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: AppConstants.textSecondaryColor,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 12),

                                  // Follower & Following Count
                                  Row(
                                    children: [
                                      Text(
                                        '${user?.followerCount ?? 0}',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppConstants.textPrimaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Followers',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: AppConstants.textSecondaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Text(
                                        '${user?.followingCount ?? 0}',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppConstants.textPrimaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Following',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: AppConstants.textSecondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 16),

                                  // Action Buttons
                                  Column(
                                    children: [
                                      // Edit Profile Button
                                      SizedBox(
                                        width: double.infinity,
                                        height: 40,
                                        child: ElevatedButton(
                                          onPressed: () {
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) => const EditProfileScreen(),
                                              ),
                                            );
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: AppConstants.primaryColor,
                                            foregroundColor: AppConstants.onPrimaryColor,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                          ),
                                          child: const Text(
                                            'Edit Profile',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),

                                      const SizedBox(height: 8),

                                      // Share Profile Button
                                      SizedBox(
                                        width: double.infinity,
                                        height: 40,
                                        child: OutlinedButton.icon(
                                          onPressed: () {
                                            _showComingSoonDialog('Share profile');
                                          },
                                          style: OutlinedButton.styleFrom(
                                            side: BorderSide(color: AppConstants.primaryColor, width: 1),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                          ),
                                          icon: Icon(
                                            Icons.share_outlined,
                                            size: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                          label: Text(
                                            'Share Profile',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingLarge),

                  // Biography Section (Conditional)
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final user = authProvider.currentUser;

                      // Only show biography section if bio exists and is not empty
                      if (user?.bio != null && user!.bio!.trim().isNotEmpty) {
                        return Column(
                          children: [
                            const SizedBox(height: 16),
                            Container(
                              width: double.infinity,
                              margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
                              padding: const EdgeInsets.all(AppConstants.paddingMedium),
                              decoration: BoxDecoration(
                                color: AppConstants.surfaceColor,
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                                border: Border.all(
                                  color: AppConstants.backgroundColor,
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'About',
                                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    user.bio!.trim(),
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: AppConstants.textPrimaryColor,
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 3),
                          ],
                        );
                      }

                      // Return empty widget if no bio (completely hidden)
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
              ],
            ),
          ),

          // Sticky Tab Bar
          SliverPersistentHeader(
            pinned: true,
            delegate: _TabBarDelegate(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppConstants.surfaceColor,
                  border: Border(
                    bottom: BorderSide(
                      color: AppConstants.backgroundColor,
                      width: 1,
                    ),
                  ),
                ),
                child: TabBar(
                  controller: _tabController,
                  labelColor: AppConstants.primaryColor,
                  unselectedLabelColor: AppConstants.textSecondaryColor,
                  indicatorColor: AppConstants.primaryColor,
                  tabs: const [
                    Tab(
                      icon: Icon(Icons.shopping_bag_outlined),
                      text: 'Products',
                    ),
                    Tab(
                      icon: Icon(Icons.grid_on),
                      text: 'Posts',
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Tab Content
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildProductsGrid(),
                _buildPostsGrid(),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildPostsGrid() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userId = authProvider.currentUser?.id ?? '';

        if (userId.isEmpty) {
          return const Center(
            child: Text('Please log in to view posts'),
          );
        }

        return FutureBuilder<List<PostModel>>(
          future: PostService.getPostsByUserId(userId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text('Error: ${snapshot.error}'),
              );
            }

            final posts = snapshot.data ?? [];

            if (posts.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.grid_on,
                      size: 64,
                      color: AppConstants.textHintColor,
                    ),
                    SizedBox(height: AppConstants.paddingMedium),
                    Text(
                      'No posts yet',
                      style: TextStyle(
                        color: AppConstants.textSecondaryColor,
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                  ],
                ),
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 2,
                mainAxisSpacing: 2,
              ),
              itemCount: posts.length,
              itemBuilder: (context, index) {
                final post = posts[index];
                return _buildPostCard(post);
              },
            );
          },
        );
      },
    );
  }

  void _handleLogout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: AppConstants.errorColor,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      await authProvider.signOut();
      // Navigation will be handled by the auth state listener
    }
  }

  void _showCoverPhotoOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickCoverImageFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _pickCoverImageFromCamera();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Remove Cover Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _removeCoverPhoto();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showProfilePhotoOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickProfileImageFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _pickProfileImageFromCamera();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Remove Profile Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _removeProfilePhoto();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Profile Photo Upload Methods
  void _pickProfileImageFromGallery() async {
    try {
      final XFile? imageFile = await ImageService.pickImageFromGallery();
      if (imageFile != null) {
        await _uploadProfileImage(imageFile);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image from gallery: $e');
    }
  }

  void _pickProfileImageFromCamera() async {
    try {
      final XFile? imageFile = await ImageService.pickImageFromCamera();
      if (imageFile != null) {
        await _uploadProfileImage(imageFile);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: $e');
    }
  }

  Future<void> _uploadProfileImage(XFile imageFile) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      _showErrorSnackBar('Please login to update profile picture');
      return;
    }

    try {
      // Show loading indicator
      _showLoadingDialog('Uploading profile picture...');

      // Upload image to Cloudinary
      final imageUrl = await ImageService.uploadProfileImage(
        imageFile: imageFile,
        userId: currentUser.id,
      );

      // Hide loading dialog
      Navigator.of(context).pop();

      if (imageUrl != null) {
        // Update user profile with new image URL
        await authProvider.updateProfile(profileImageUrl: imageUrl);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('Profile picture updated successfully!'),
                ],
              ),
              backgroundColor: AppConstants.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        _showErrorSnackBar('Failed to upload profile picture. Please try again.');
      }
    } catch (e) {
      // Hide loading dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('Error uploading profile picture: $e');
    }
  }

  void _removeProfilePhoto() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      _showErrorSnackBar('Please login to remove profile picture');
      return;
    }

    try {
      // Show confirmation dialog
      final shouldRemove = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Remove Profile Picture'),
            content: const Text('Are you sure you want to remove your profile picture?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                ),
                child: const Text('Remove'),
              ),
            ],
          );
        },
      );

      if (shouldRemove == true) {
        // Show loading indicator
        _showLoadingDialog('Removing profile picture...');

        // Update user profile to remove image URL
        await authProvider.updateProfile(profileImageUrl: null);

        // Hide loading dialog
        Navigator.of(context).pop();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('Profile picture removed successfully!'),
                ],
              ),
              backgroundColor: AppConstants.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('Error removing profile picture: $e');
    }
  }

  // Cover Photo Upload Methods
  void _pickCoverImageFromGallery() async {
    try {
      final XFile? imageFile = await ImageService.pickImageFromGallery();
      if (imageFile != null) {
        await _uploadCoverImage(imageFile);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image from gallery: $e');
    }
  }

  void _pickCoverImageFromCamera() async {
    try {
      final XFile? imageFile = await ImageService.pickImageFromCamera();
      if (imageFile != null) {
        await _uploadCoverImage(imageFile);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: $e');
    }
  }

  Future<void> _uploadCoverImage(XFile imageFile) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      _showErrorSnackBar('Please login to update cover photo');
      return;
    }

    try {
      // Show loading indicator
      _showLoadingDialog('Uploading cover photo...');

      // Upload image to Cloudinary
      final imageUrl = await ImageService.uploadCoverImage(
        imageFile: imageFile,
        userId: currentUser.id,
      );

      // Hide loading dialog
      Navigator.of(context).pop();

      if (imageUrl != null) {
        // Update user profile with new cover image URL
        await authProvider.updateProfile(coverImageUrl: imageUrl);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('Cover photo updated successfully!'),
                ],
              ),
              backgroundColor: AppConstants.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        _showErrorSnackBar('Failed to upload cover photo. Please try again.');
      }
    } catch (e) {
      // Hide loading dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('Error uploading cover photo: $e');
    }
  }

  void _removeCoverPhoto() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      _showErrorSnackBar('Please login to remove cover photo');
      return;
    }

    try {
      // Show confirmation dialog
      final shouldRemove = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Remove Cover Photo'),
            content: const Text('Are you sure you want to remove your cover photo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                ),
                child: const Text('Remove'),
              ),
            ],
          );
        },
      );

      if (shouldRemove == true) {
        // Show loading indicator
        _showLoadingDialog('Removing cover photo...');

        // Update user profile to remove cover image URL
        await authProvider.updateProfile(coverImageUrl: null);

        // Hide loading dialog
        Navigator.of(context).pop();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('Cover photo removed successfully!'),
                ],
              ),
              backgroundColor: AppConstants.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      _showErrorSnackBar('Error removing cover photo: $e');
    }
  }

  // Helper Methods
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Expanded(child: Text(message)),
            ],
          ),
        );
      },
    );
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: AppConstants.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Coming Soon'),
          content: Text('$feature feature will be available soon!'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  String _getFormattedDate(DateTime? date) {
    if (date == null) return 'Recently';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return 'Recently';
    }
  }

  String _formatJoiningDate(DateTime? date) {
    if (date == null) return 'Recently';

    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.year}';
  }



  void _viewProfilePhotoFullscreen(String? imageUrl) {
    if (imageUrl == null) return;

    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.black87,
        pageBuilder: (context, animation, secondaryAnimation) {
          return FadeTransition(
            opacity: animation,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    onPressed: _showProfilePhotoOptions,
                  ),
                ],
              ),
              body: Center(
                child: Hero(
                  tag: 'profile_photo_fullscreen',
                  child: InteractiveViewer(
                    child: CircleAvatar(
                      radius: 150,
                      backgroundImage: CachedNetworkImageProvider(imageUrl),
                      onBackgroundImageError: (error, stackTrace) {
                        // Handle error
                      },
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Get user statistics
  Future<Map<String, int>> _getUserStats(String userId) async {
    if (userId.isEmpty) return {'posts': 0, 'products': 0};

    try {
      final posts = await PostService.getPostsByUserId(userId);
      final products = await ProductService.getProductsBySellerId(userId);

      return {
        'posts': posts.length,
        'products': products.length,
      };
    } catch (e) {
      print('Error getting user stats: $e');
      return {'posts': 0, 'products': 0};
    }
  }

  Widget _buildProductsGrid() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userId = authProvider.currentUser?.id ?? '';

        if (userId.isEmpty) {
          return const Center(
            child: Text('Please log in to view products'),
          );
        }

        return FutureBuilder<List<ProductModel>>(
          future: ProductService.getProductsBySellerId(userId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text('Error: ${snapshot.error}'),
              );
            }

            final products = snapshot.data ?? [];

            if (products.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_bag_outlined,
                      size: 64,
                      color: AppConstants.textHintColor,
                    ),
                    SizedBox(height: AppConstants.paddingMedium),
                    Text(
                      'No products yet',
                      style: TextStyle(
                        color: AppConstants.textSecondaryColor,
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                  ],
                ),
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: AppConstants.paddingSmall,
                mainAxisSpacing: AppConstants.paddingSmall,
                childAspectRatio: 0.75,
              ),
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return _buildProductCard(product);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildProductCard(ProductModel product) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to product detail
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppConstants.backgroundColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.borderRadiusMedium),
                    topRight: Radius.circular(AppConstants.borderRadiusMedium),
                  ),
                ),
                child: product.hasImages
                    ? ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(AppConstants.borderRadiusMedium),
                          topRight: Radius.circular(AppConstants.borderRadiusMedium),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: product.primaryImageUrl!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade200,
                            child: const Center(child: CircularProgressIndicator()),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey.shade200,
                            child: const Icon(Icons.error),
                          ),
                        ),
                      )
                    : const Center(
                        child: Icon(
                          Icons.shopping_bag_outlined,
                          size: AppConstants.iconSizeLarge,
                          color: AppConstants.textHintColor,
                        ),
                      ),
              ),
            ),
            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppConstants.paddingXSmall),
                    Text(
                      product.formattedPrice,
                      style: const TextStyle(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostCard(PostModel post) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: InkWell(
        onTap: () {
          // TODO: View post detail
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        child: post.hasImages
            ? ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                child: CachedNetworkImage(
                  imageUrl: post.imageUrls.first,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade200,
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey.shade200,
                    child: const Icon(Icons.error),
                  ),
                ),
              )
            : Center(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  child: Text(
                    post.content,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeXSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                    maxLines: 4,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
      ),
    );
  }
}

// Custom delegate for sticky tab bar
class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _TabBarDelegate({required this.child});

  @override
  double get minExtent => 72.0; // Minimum height when collapsed

  @override
  double get maxExtent => 72.0; // Maximum height when expanded

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: maxExtent,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
