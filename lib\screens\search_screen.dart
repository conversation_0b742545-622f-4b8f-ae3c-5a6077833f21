import 'package:flutter/material.dart';
import 'dart:async';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../widgets/product_card.dart';
import '../utils/search_history.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<ProductModel> _searchResults = [];
  List<String> _searchSuggestions = [];
  bool _isLoading = false;
  bool _showSuggestions = false;
  Timer? _debounceTimer;
  final FocusNode _searchFocusNode = FocusNode();
  List<String> _recentSearches = [];
  List<String> _popularSearches = [
    'Electronics',
    'Fashion',
    'Books',
    'Home & Garden',
    'Sports',
    'Beauty',
    'Toys',
    'Automotive'
  ];

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus && _searchQuery.isEmpty) {
        setState(() {
          _showSuggestions = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    _searchFocusNode.dispose();
    super.dispose();
  }

  // Load recent searches from local storage
  void _loadRecentSearches() async {
    try {
      final searches = await SearchHistory.getRecentSearches();
      if (mounted) {
        setState(() {
          _recentSearches = searches;
        });
      }
    } catch (e) {
      print('Error loading recent searches: $e');
    }
  }

  // Save search to recent searches
  void _saveToRecentSearches(String query) async {
    if (query.trim().isEmpty) return;

    try {
      await SearchHistory.addToRecentSearches(query);
      final searches = await SearchHistory.getRecentSearches();
      if (mounted) {
        setState(() {
          _recentSearches = searches;
        });
      }
    } catch (e) {
      print('Error saving recent search: $e');
    }
  }

  // Debounced search function
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _showSuggestions = query.isNotEmpty && query.length >= 2;
    });

    _debounceTimer?.cancel();

    if (query.length >= 2) {
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        _performSearch(query);
        _generateSuggestions(query);
      });
    } else if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
        _searchSuggestions.clear();
        _showSuggestions = true;
      });
    }
  }

  // Perform actual search
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await ProductService.searchProducts(query);

      if (mounted) {
        setState(() {
          _searchResults = results;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Search error: $e');
    }
  }

  // Generate search suggestions
  Future<void> _generateSuggestions(String query) async {
    if (query.length < 2) return;

    try {
      final suggestions = await ProductService.getSearchSuggestions(query);

      if (mounted) {
        setState(() {
          _searchSuggestions = suggestions;
        });
      }
    } catch (e) {
      print('Suggestion error: $e');
    }
  }

  // Handle suggestion tap
  void _onSuggestionTap(String suggestion) {
    _searchController.text = suggestion;
    _saveToRecentSearches(suggestion);
    _onSearchChanged(suggestion);
    _searchFocusNode.unfocus();
    setState(() {
      _showSuggestions = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Products'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              color: AppConstants.surfaceColor,
              border: Border(
                bottom: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: 'Search for products...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                            _searchResults.clear();
                            _searchSuggestions.clear();
                            _showSuggestions = true;
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: AppConstants.backgroundColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  borderSide: BorderSide.none,
                ),
              ),
              onChanged: _onSearchChanged,
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _saveToRecentSearches(value);
                  _performSearch(value);
                  _searchFocusNode.unfocus();
                  setState(() {
                    _showSuggestions = false;
                  });
                }
              },
            ),
          ),

          // Search Content
          Expanded(
            child: _showSuggestions
                ? _buildSuggestionsView()
                : _searchQuery.isEmpty
                    ? _buildEmptyState()
                    : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  // Build suggestions view
  Widget _buildSuggestionsView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search suggestions based on current query
          if (_searchSuggestions.isNotEmpty) ...[
            Text(
              'Suggestions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            ..._searchSuggestions.map((suggestion) => _buildSuggestionItem(
              suggestion,
              Icons.search,
              () => _onSuggestionTap(suggestion),
            )),
            const SizedBox(height: AppConstants.paddingLarge),
          ],

          // Recent searches
          if (_recentSearches.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Searches',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () async {
                    try {
                      await SearchHistory.clearRecentSearches();
                      setState(() {
                        _recentSearches.clear();
                      });
                    } catch (e) {
                      print('Error clearing recent searches: $e');
                    }
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            ..._recentSearches.map((search) => _buildSuggestionItem(
              search,
              Icons.history,
              () => _onSuggestionTap(search),
              trailing: IconButton(
                icon: const Icon(Icons.close, size: 16),
                onPressed: () async {
                  try {
                    await SearchHistory.removeFromRecentSearches(search);
                    setState(() {
                      _recentSearches.remove(search);
                    });
                  } catch (e) {
                    print('Error removing recent search: $e');
                  }
                },
              ),
            )),
            const SizedBox(height: AppConstants.paddingLarge),
          ],

          // Popular searches
          Text(
            'Popular Searches',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children: _popularSearches.map((search) => _buildPopularSearchChip(search)).toList(),
          ),
        ],
      ),
    );
  }

  // Build suggestion item
  Widget _buildSuggestionItem(
    String text,
    IconData icon,
    VoidCallback onTap, {
    Widget? trailing,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
        child: Row(
          children: [
            Icon(
              icon,
              size: AppConstants.iconSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  // Build popular search chip
  Widget _buildPopularSearchChip(String text) {
    return ActionChip(
      label: Text(text),
      onPressed: () => _onSuggestionTap(text),
      backgroundColor: AppConstants.backgroundColor,
      side: BorderSide(color: AppConstants.primaryColor.withOpacity(0.3)),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: AppConstants.iconSizeXLarge * 2,
            color: AppConstants.textHintColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Search for products',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Find amazing products from our marketplace',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textHintColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: AppConstants.iconSizeXLarge * 2,
              color: AppConstants.textHintColor,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No products found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Try searching with different keywords',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textHintColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Search results header
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              Text(
                '${_searchResults.length} products found',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const Spacer(),
              // Sort button
              TextButton.icon(
                onPressed: () {
                  // TODO: Implement sort functionality
                },
                icon: const Icon(Icons.sort, size: 16),
                label: const Text('Sort'),
              ),
            ],
          ),
        ),

        // Products grid
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: AppConstants.paddingMedium,
              mainAxisSpacing: AppConstants.paddingMedium,
            ),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              return ProductCard(product: _searchResults[index]);
            },
          ),
        ),
      ],
    );
  }

}
