import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../services/post_service.dart';
import '../services/product_service.dart';
import '../services/user_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/post_card.dart';
import 'product_detail_screen.dart';

class UserProfileScreen extends StatefulWidget {
  final String userId;
  final String? initialUserName; // Optional: for display while loading

  const UserProfileScreen({
    super.key,
    required this.userId,
    this.initialUserName,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UserModel? _user;
  bool _isLoading = true;
  String? _error;
  bool _isFollowing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserProfile();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final user = await UserService.getUserById(widget.userId);
      
      setState(() {
        _user = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_user?.displayName ?? widget.initialUserName ?? 'Profile'),
        centerTitle: true,
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'copy_link',
                child: Row(
                  children: [
                    Icon(Icons.link, size: 20),
                    SizedBox(width: 12),
                    Text('Copy Link'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'block',
                child: Row(
                  children: [
                    Icon(Icons.block, size: 20),
                    SizedBox(width: 12),
                    Text('Block'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, size: 20),
                    SizedBox(width: 12),
                    Text('Report'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load profile',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadUserProfile,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _user == null
                  ? const Center(child: Text('User not found'))
                  : CustomScrollView(
                      slivers: [
                        // Cover Photo and Profile Section (Scrollable)
                        SliverToBoxAdapter(
                          child: Column(
                            children: [
                              // Cover Photo Section
                              Container(
                                height: 180,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: AppConstants.backgroundColor,
                                  image: _user!.coverImageUrl != null
                                      ? DecorationImage(
                                          image: CachedNetworkImageProvider(_user!.coverImageUrl!),
                                          fit: BoxFit.cover,
                                        )
                                      : null,
                                ),
                                child: _user!.coverImageUrl == null
                                    ? Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              AppConstants.primaryColor.withOpacity(0.8),
                                              AppConstants.primaryColor.withOpacity(0.6),
                                            ],
                                          ),
                                        ),
                                      )
                                    : null,
                              ),

                              // Profile Section
                              Container(
                                color: AppConstants.surfaceColor,
                                child: Column(
                                  children: [
                                // Profile Section - Picture Higher, Name Lower Position
                                Transform.translate(
                                  offset: const Offset(0, -80),
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8, right: AppConstants.paddingLarge),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // Round Profile Picture
                                        Container(
                                          width: 120,
                                          height: 120,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: AppConstants.primaryColor,
                                              width: 3,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(0.1),
                                                blurRadius: 10,
                                                offset: const Offset(0, 5),
                                              ),
                                            ],
                                          ),
                                          child: ClipOval(
                                            child: _user!.profileImageUrl != null
                                                ? CachedNetworkImage(
                                                    imageUrl: _user!.profileImageUrl!,
                                                    fit: BoxFit.cover,
                                                    placeholder: (context, url) => Container(
                                                      color: AppConstants.backgroundColor,
                                                      child: const Center(
                                                        child: CircularProgressIndicator(),
                                                      ),
                                                    ),
                                                    errorWidget: (context, url, error) => Container(
                                                      color: AppConstants.primaryColor,
                                                      child: Center(
                                                        child: Text(
                                                          _user!.displayName.isNotEmpty
                                                              ? _user!.displayName[0].toUpperCase()
                                                              : 'U',
                                                          style: const TextStyle(
                                                            fontSize: 45,
                                                            fontWeight: FontWeight.bold,
                                                            color: AppConstants.onPrimaryColor,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  )
                                                : Container(
                                                    color: AppConstants.primaryColor,
                                                    child: Center(
                                                      child: Text(
                                                        _user!.displayName.isNotEmpty
                                                            ? _user!.displayName[0].toUpperCase()
                                                            : 'U',
                                                        style: const TextStyle(
                                                          fontSize: 45,
                                                          fontWeight: FontWeight.bold,
                                                          color: AppConstants.onPrimaryColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                          ),
                                        ),

                                        const SizedBox(height: AppConstants.paddingMedium),

                                        // User Info Below Profile Picture
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            // Display Name with Verification Badge
                                            Row(
                                              children: [
                                                Flexible(
                                                  child: Text(
                                                    _user!.displayName,
                                                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                                      fontWeight: FontWeight.bold,
                                                      color: AppConstants.textPrimaryColor,
                                                    ),
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ),
                                                if (_user!.isVerified == true)
                                                  const Padding(
                                                    padding: EdgeInsets.only(left: 4),
                                                    child: Icon(
                                                      Icons.verified,
                                                      color: Colors.blue,
                                                      size: 20,
                                                    ),
                                                  ),
                                              ],
                                            ),

                                            const SizedBox(height: 4),

                                            // Username and Joining Date
                                            Row(
                                              children: [
                                                Text(
                                                  '@${_user!.username}',
                                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                    color: AppConstants.textSecondaryColor,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  '•',
                                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                    color: AppConstants.textSecondaryColor,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  'Joined ${_formatJoiningDate(_user!.createdAt)}',
                                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                    color: AppConstants.textSecondaryColor,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ],
                                            ),



                                            const SizedBox(height: 12),

                                            // Follower & Following Count
                                            Row(
                                              children: [
                                                Text(
                                                  '120', // TODO: Get from user data
                                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                    color: AppConstants.textPrimaryColor,
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'Followers',
                                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                    color: AppConstants.textSecondaryColor,
                                                  ),
                                                ),
                                                const SizedBox(width: 16),
                                                Text(
                                                  '89', // TODO: Get from user data
                                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                    color: AppConstants.textPrimaryColor,
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'Following',
                                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                    color: AppConstants.textSecondaryColor,
                                                  ),
                                                ),
                                              ],
                                            ),

                                            const SizedBox(height: 16),

                                            // Action Buttons
                                            Column(
                                              children: [
                                                // Follow Button
                                                SizedBox(
                                                  width: double.infinity,
                                                  height: 40,
                                                  child: ElevatedButton(
                                                    onPressed: () => _toggleFollow(),
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor: _isFollowing
                                                          ? AppConstants.backgroundColor
                                                          : AppConstants.primaryColor,
                                                      foregroundColor: _isFollowing
                                                          ? AppConstants.textPrimaryColor
                                                          : AppConstants.onPrimaryColor,
                                                      side: _isFollowing
                                                          ? BorderSide(color: AppConstants.primaryColor, width: 1)
                                                          : null,
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(8),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      _isFollowing ? 'Following' : 'Follow',
                                                      style: const TextStyle(
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                ),

                                                const SizedBox(height: 8),

                                                // Chat Now Button
                                                SizedBox(
                                                  width: double.infinity,
                                                  height: 40,
                                                  child: OutlinedButton.icon(
                                                    onPressed: () => _startChat(),
                                                    style: OutlinedButton.styleFrom(
                                                      side: BorderSide(color: AppConstants.primaryColor, width: 1),
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(8),
                                                      ),
                                                    ),
                                                    icon: Icon(
                                                      Icons.chat_bubble_outline,
                                                      size: 18,
                                                      color: AppConstants.primaryColor,
                                                    ),
                                                    label: Text(
                                                      'Chat Now',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.w600,
                                                        color: AppConstants.primaryColor,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                // Biography Section (Conditional)
                                if (_user!.bio != null && _user!.bio!.isNotEmpty) ...[
                                  const SizedBox(height: 3),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
                                    child: Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                                      decoration: BoxDecoration(
                                        color: AppConstants.backgroundColor,
                                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                                        border: Border.all(
                                          color: AppConstants.primaryColor.withOpacity(0.2),
                                          width: 1,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.info_outline,
                                                size: 18,
                                                color: AppConstants.primaryColor,
                                              ),
                                              const SizedBox(width: 6),
                                              Text(
                                                'About',
                                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                  color: AppConstants.primaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            _user!.bio!,
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              color: AppConstants.textPrimaryColor,
                                              height: 1.4,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 3),
                                ],

                                // Action Buttons (Follow/Message)
                                _buildActionButtons(),

                                const SizedBox(height: 3),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Sticky Tab Bar
                        SliverPersistentHeader(
                          pinned: true,
                          delegate: _TabBarDelegate(
                            child: Container(
                              decoration: const BoxDecoration(
                                color: AppConstants.surfaceColor,
                                border: Border(
                                  bottom: BorderSide(
                                    color: AppConstants.backgroundColor,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: TabBar(
                                controller: _tabController,
                                labelColor: AppConstants.primaryColor,
                                unselectedLabelColor: AppConstants.textSecondaryColor,
                                indicatorColor: AppConstants.primaryColor,
                                tabs: const [
                                  Tab(
                                    icon: Icon(Icons.shopping_bag_outlined),
                                    text: 'Products',
                                  ),
                                  Tab(
                                    icon: Icon(Icons.grid_on),
                                    text: 'Posts',
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // Tab Content
                        SliverFillRemaining(
                          child: TabBarView(
                            controller: _tabController,
                            children: [
                              _buildProductsGrid(),
                              _buildPostsGrid(),
                            ],
                          ),
                        ),
                      ],
                    ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final isOwnProfile = currentUser?.id == widget.userId;

    if (isOwnProfile) {
      return const SizedBox.shrink(); // Don't show action buttons for own profile
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                // TODO: Implement follow functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Follow functionality coming soon!')),
                );
              },
              icon: const Icon(Icons.person_add_outlined),
              label: const Text('Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: AppConstants.onPrimaryColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: Implement message functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Message functionality coming soon!')),
                );
              },
              icon: const Icon(Icons.message_outlined),
              label: const Text('Message'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppConstants.primaryColor,
                side: const BorderSide(color: AppConstants.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildProductsGrid() {
    if (_user == null) return const SizedBox.shrink();

    return FutureBuilder<List<ProductModel>>(
      future: ProductService.getProductsBySellerId(_user!.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Error loading products: ${snapshot.error}'),
          );
        }

        final products = snapshot.data ?? [];

        if (products.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_bag_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No products yet',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: AppConstants.paddingMedium,
                  mainAxisSpacing: AppConstants.paddingMedium,
                  childAspectRatio: 0.8,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final product = products[index];
                    return Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: product.imageUrls.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: product.imageUrls.first,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              placeholder: (context, url) => Container(
                                color: Colors.grey.shade200,
                                child: const Center(child: CircularProgressIndicator()),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey.shade200,
                                child: const Icon(Icons.error),
                              ),
                            )
                          : Container(
                              color: Colors.grey.shade200,
                              child: const Icon(Icons.image, size: 50),
                            ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '৳${product.price}',
                          style: TextStyle(
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
                  },
                  childCount: products.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPostsGrid() {
    if (_user == null) return const SizedBox.shrink();

    return FutureBuilder<List<PostModel>>(
      future: PostService.getPostsByUserId(_user!.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Error loading posts: ${snapshot.error}'),
          );
        }

        final posts = snapshot.data ?? [];

        if (posts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.grid_on,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No posts yet',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final post = posts[index];
                    return PostCard(
                      post: post,
                      showUserInfo: true, // Show user info like in feed
                    );
                  },
                  childCount: posts.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatJoiningDate(DateTime joinDate) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[joinDate.month - 1]} ${joinDate.year}';
  }

  Future<void> _toggleFollow() async {
    if (_user == null) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to follow users'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      await UserService.toggleFollow(
        currentUserId: currentUser.id,
        targetUserId: _user!.id,
      );

      setState(() {
        _isFollowing = !_isFollowing;
        // TODO: Update follower count when UserModel supports it
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isFollowing ? 'Following ${_user!.displayName}' : 'Unfollowed ${_user!.displayName}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _startChat() {
    if (_user == null) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to start a chat'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    // TODO: Navigate to chat screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting chat with ${_user!.displayName}...'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_link':
        _copyProfileLink();
        break;
      case 'block':
        _blockUser();
        break;
      case 'report':
        _reportUser();
        break;
    }
  }

  void _copyProfileLink() async {
    if (_user == null) return;

    // Create profile link
    final profileLink = 'https://amalpoint.com/profile/${_user!.id}';

    try {
      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: profileLink));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile link copied to clipboard'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to copy link'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _blockUser() {
    if (_user == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Block User'),
          content: Text('Are you sure you want to block ${_user!.displayName}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual blocking functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${_user!.displayName} has been blocked'),
                    backgroundColor: AppConstants.errorColor,
                  ),
                );
              },
              child: const Text('Block', style: TextStyle(color: AppConstants.errorColor)),
            ),
          ],
        );
      },
    );
  }

  void _reportUser() {
    if (_user == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Report User'),
          content: Text('Report ${_user!.displayName} for inappropriate behavior?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual reporting functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${_user!.displayName} has been reported'),
                    backgroundColor: AppConstants.warningColor,
                  ),
                );
              },
              child: const Text('Report', style: TextStyle(color: AppConstants.errorColor)),
            ),
          ],
        );
      },
    );
  }
}

// Custom delegate for sticky tab bar
class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _TabBarDelegate({required this.child});

  @override
  double get minExtent => 72.0; // Minimum height when collapsed

  @override
  double get maxExtent => 72.0; // Maximum height when expanded

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: maxExtent,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
