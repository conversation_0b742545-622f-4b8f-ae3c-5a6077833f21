import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:cloudinary_public/cloudinary_public.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

class CloudinaryService {
  static const String _cloudName = 'doxd3bscf';
  static const String _apiKey = '251519664785658';
  static const String _apiSecret = 'Y3-ZEUAZ5b121-gr9UBbzfV006E';
  
  static final CloudinaryPublic _cloudinary = CloudinaryPublic(
    _cloudName,
    'amarpoint', // Upload preset name
    cache: false,
  );

  /// Upload image file to Cloudinary
  /// Returns the secure URL of the uploaded image
  static Future<String?> uploadImage({
    required dynamic imageFile, // Can be File or Uint8List
    required String folder,
    String? publicId,
    int? quality,
    int? width,
    int? height,
  }) async {
    try {
      print('CloudinaryService: Starting image upload to folder: $folder');
      print('CloudinaryService: Image file type: ${imageFile.runtimeType}');
      print('CloudinaryService: Using upload preset: amarpoint');

      CloudinaryResponse response;

      if (kIsWeb && imageFile is Uint8List) {
        print('CloudinaryService: Uploading from bytes (Web)');
        // For web platform, upload from bytes
        response = await _cloudinary.uploadFile(
          CloudinaryFile.fromBytesData(
            imageFile,
            identifier: publicId ?? 'image_${DateTime.now().millisecondsSinceEpoch}',
            folder: folder,
          ),
        );
      } else if (imageFile is File) {
        print('CloudinaryService: Uploading from file (Mobile): ${imageFile.path}');
        // For mobile platforms, upload from file
        response = await _cloudinary.uploadFile(
          CloudinaryFile.fromFile(
            imageFile.path,
            folder: folder,
            publicId: publicId,
          ),
        );
      } else {
        throw Exception('Unsupported image file type: ${imageFile.runtimeType}');
      }

      print('CloudinaryService: Upload successful! URL: ${response.secureUrl}');
      return response.secureUrl;
    } catch (e) {
      print('CloudinaryService: Error uploading image to Cloudinary: $e');
      print('CloudinaryService: Error type: ${e.runtimeType}');
      if (e.toString().contains('401')) {
        print('CloudinaryService: Authentication error - check API credentials');
      } else if (e.toString().contains('400')) {
        print('CloudinaryService: Bad request - check upload preset and parameters');
      }
      return null;
    }
  }

  /// Upload profile image with optimized settings
  static Future<String?> uploadProfileImage({
    required dynamic imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      folder: 'profile_images',
      publicId: 'profile_$userId',
      quality: 80,
      width: 400,
      height: 400,
    );
  }

  /// Upload cover image with optimized settings
  static Future<String?> uploadCoverImage({
    required dynamic imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      folder: 'cover_images',
      publicId: 'cover_$userId',
      quality: 85,
      width: 1200,
      height: 400,
    );
  }

  /// Upload product image with optimized settings
  static Future<String?> uploadProductImage({
    required dynamic imageFile,
    required String productId,
    int? index,
  }) async {
    final publicId = index != null 
        ? 'product_${productId}_$index' 
        : 'product_$productId';
        
    return await uploadImage(
      imageFile: imageFile,
      folder: 'product_images',
      publicId: publicId,
      quality: 85,
      width: 800,
      height: 800,
    );
  }

  /// Upload post image with optimized settings
  static Future<String?> uploadPostImage({
    required dynamic imageFile,
    required String postId,
    int? index,
  }) async {
    final publicId = index != null 
        ? 'post_${postId}_$index' 
        : 'post_$postId';
        
    return await uploadImage(
      imageFile: imageFile,
      folder: 'post_images',
      publicId: publicId,
      quality: 80,
      width: 1080,
      height: 1080,
    );
  }

  /// Upload multiple images (for products or posts)
  static Future<List<String>> uploadMultipleImages({
    required List<dynamic> imageFiles,
    required String folder,
    required String basePublicId,
    int? quality,
    int? width,
    int? height,
  }) async {
    List<String> uploadedUrls = [];

    print('CloudinaryService: Starting upload of ${imageFiles.length} images');

    for (int i = 0; i < imageFiles.length; i++) {
      print('CloudinaryService: Uploading image ${i + 1}/${imageFiles.length}');

      final url = await uploadImage(
        imageFile: imageFiles[i],
        folder: folder,
        publicId: '${basePublicId}_$i',
        quality: quality,
        width: width,
        height: height,
      );

      if (url != null) {
        uploadedUrls.add(url);
        print('CloudinaryService: Successfully uploaded image ${i + 1}: $url');
      } else {
        print('CloudinaryService: Failed to upload image ${i + 1}');
      }
    }

    print('CloudinaryService: Upload completed. ${uploadedUrls.length}/${imageFiles.length} images uploaded successfully');
    return uploadedUrls;
  }

  /// Extract public ID from Cloudinary URL
  static String? extractPublicIdFromUrl(String imageUrl) {
    try {
      if (!imageUrl.contains('cloudinary.com')) {
        return null;
      }

      // Example URL: https://res.cloudinary.com/doxd3bscf/image/upload/v1234567890/post_images/post_abc123_0.jpg
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // Find the upload segment
      int uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return null;

      // The public ID is everything after version (if present) or after upload
      List<String> publicIdParts = [];
      for (int i = uploadIndex + 1; i < pathSegments.length; i++) {
        String segment = pathSegments[i];
        // Skip version segment (starts with 'v' followed by numbers)
        if (i == uploadIndex + 1 && segment.startsWith('v') && segment.length > 1) {
          bool isVersion = true;
          for (int j = 1; j < segment.length; j++) {
            if (!segment[j].contains(RegExp(r'[0-9]'))) {
              isVersion = false;
              break;
            }
          }
          if (isVersion) continue;
        }
        publicIdParts.add(segment);
      }

      if (publicIdParts.isEmpty) return null;

      // Join with '/' and remove file extension from last part
      String publicId = publicIdParts.join('/');

      // Remove file extension
      int lastDotIndex = publicId.lastIndexOf('.');
      if (lastDotIndex != -1) {
        publicId = publicId.substring(0, lastDotIndex);
      }

      return publicId;
    } catch (e) {
      print('Error extracting public ID from URL: $e');
      return null;
    }
  }

  /// Delete multiple images from Cloudinary URLs
  static Future<List<String>> deleteMultipleImages(List<String> imageUrls) async {
    List<String> deletedPublicIds = [];

    print('CloudinaryService: Starting deletion of ${imageUrls.length} images');

    for (String imageUrl in imageUrls) {
      final publicId = extractPublicIdFromUrl(imageUrl);
      if (publicId != null) {
        print('CloudinaryService: Extracted public ID: $publicId from URL: $imageUrl');

        // Delete the image using Cloudinary Admin API
        final success = await _realImageDeletion(publicId);
        if (success) {
          deletedPublicIds.add(publicId);
        }
      } else {
        print('CloudinaryService: Could not extract public ID from URL: $imageUrl');
      }
    }

    print('CloudinaryService: Deletion completed. ${deletedPublicIds.length}/${imageUrls.length} images marked for deletion');
    return deletedPublicIds;
  }

  /// Generate signature for Cloudinary Admin API
  static String _generateSignature(Map<String, String> params, String apiSecret) {
    // Remove signature and api_key from params for signing (if they exist)
    final paramsForSigning = Map<String, String>.from(params);
    paramsForSigning.remove('signature');
    paramsForSigning.remove('api_key');

    // Sort parameters by key (case-sensitive)
    final sortedKeys = paramsForSigning.keys.toList()..sort();

    // Create query string with URL encoding
    final queryParts = <String>[];
    for (final key in sortedKeys) {
      final value = paramsForSigning[key]!;
      queryParts.add('$key=$value');
    }

    final queryString = queryParts.join('&');

    // Add API secret at the end
    final stringToSign = '$queryString$apiSecret';

    // Generate SHA1 hash
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  /// Delete image using Cloudinary Admin API
  static Future<bool> _realImageDeletion(String publicId) async {
    try {
      print('CloudinaryService: [REAL] Deleting image with public ID: $publicId');

      // Prepare parameters for API call (only parameters that need to be signed)
      final timestamp = (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
      final paramsToSign = {
        'public_id': publicId,
        'timestamp': timestamp,
      };

      // Generate signature
      final signature = _generateSignature(paramsToSign, _apiSecret);

      // Prepare final parameters for API call
      final finalParams = {
        'public_id': publicId,
        'timestamp': timestamp,
        'api_key': _apiKey,
        'signature': signature,
      };

      // Make API call to Cloudinary
      final url = 'https://api.cloudinary.com/v1_1/$_cloudName/image/destroy';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: finalParams,
      );

      print('CloudinaryService: API Response Status: ${response.statusCode}');
      print('CloudinaryService: API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final result = responseData['result'];

        if (result == 'ok') {
          print('CloudinaryService: [REAL] Successfully deleted image: $publicId');
          return true;
        } else if (result == 'not found') {
          print('CloudinaryService: [REAL] Image not found (may already be deleted): $publicId');
          return true; // Consider this as success since the image is gone
        } else {
          print('CloudinaryService: [REAL] Unexpected result: $result for image: $publicId');
          return false;
        }
      } else {
        print('CloudinaryService: [REAL] API call failed with status ${response.statusCode}: ${response.body}');
        return false;
      }
    } catch (e) {
      print('CloudinaryService: [REAL] Error deleting image $publicId: $e');
      return false;
    }
  }

  /// Delete image from Cloudinary using Admin API
  static Future<bool> deleteImage(String publicId) async {
    try {
      return await _realImageDeletion(publicId);
    } catch (e) {
      print('Error deleting image from Cloudinary: $e');
      return false;
    }
  }

  /// Get optimized image URL with transformations
  static String getOptimizedImageUrl({
    required String imageUrl,
    int? width,
    int? height,
    int? quality,
    String? format,
  }) {
    if (!imageUrl.contains('cloudinary.com')) {
      return imageUrl; // Return original URL if not a Cloudinary URL
    }
    
    String transformations = '';
    List<String> params = [];
    
    if (width != null) params.add('w_$width');
    if (height != null) params.add('h_$height');
    if (quality != null) params.add('q_$quality');
    if (format != null) params.add('f_$format');
    
    if (params.isNotEmpty) {
      transformations = params.join(',');
      // Insert transformations into the URL
      return imageUrl.replaceFirst('/upload/', '/upload/$transformations/');
    }
    
    return imageUrl;
  }

  /// Generate thumbnail URL
  static String getThumbnailUrl(String imageUrl, {int size = 150}) {
    return getOptimizedImageUrl(
      imageUrl: imageUrl,
      width: size,
      height: size,
      quality: 70,
      format: 'webp',
    );
  }

  /// Generate responsive image URLs for different screen sizes
  static Map<String, String> getResponsiveImageUrls(String imageUrl) {
    return {
      'thumbnail': getThumbnailUrl(imageUrl, size: 150),
      'small': getOptimizedImageUrl(imageUrl: imageUrl, width: 400, quality: 80),
      'medium': getOptimizedImageUrl(imageUrl: imageUrl, width: 800, quality: 85),
      'large': getOptimizedImageUrl(imageUrl: imageUrl, width: 1200, quality: 90),
      'original': imageUrl,
    };
  }



  /// Test Cloudinary connection and configuration
  static Future<bool> testConnection() async {
    try {
      print('CloudinaryService: Testing connection...');
      print('CloudinaryService: Cloud name: $_cloudName');
      print('CloudinaryService: Upload preset: amarpoint');

      // Create a simple test image (1x1 pixel)
      final testImageBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8B, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);

      final response = await _cloudinary.uploadFile(
        CloudinaryFile.fromBytesData(
          testImageBytes,
          identifier: 'test_connection_${DateTime.now().millisecondsSinceEpoch}',
          folder: 'test',
        ),
      );

      print('CloudinaryService: Connection test successful! URL: ${response.secureUrl}');
      return true;
    } catch (e) {
      print('CloudinaryService: Connection test failed: $e');
      return false;
    }
  }
}
