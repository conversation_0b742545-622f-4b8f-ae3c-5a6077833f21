import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'cloudinary_service.dart';

class ImageService {
  static final ImagePicker _picker = ImagePicker();

  /// Pick image from gallery
  static Future<XFile?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      print('Error picking image from gallery: $e');
      return null;
    }
  }

  /// Pick image from camera
  static Future<XFile?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      print('Error picking image from camera: $e');
      return null;
    }
  }

  /// Pick multiple images from gallery
  static Future<List<XFile>?> pickMultipleImages({int? maxImages}) async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      
      if (maxImages != null && images.length > maxImages) {
        return images.take(maxImages).toList();
      }
      
      return images;
    } catch (e) {
      print('Error picking multiple images: $e');
      return null;
    }
  }

  /// Convert XFile to appropriate format for upload
  static Future<dynamic> _prepareImageForUpload(XFile imageFile) async {
    if (kIsWeb) {
      // For web, return bytes
      return await imageFile.readAsBytes();
    } else {
      // For mobile, return File
      return File(imageFile.path);
    }
  }

  /// Upload profile image
  static Future<String?> uploadProfileImage({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      final preparedImage = await _prepareImageForUpload(imageFile);
      return await CloudinaryService.uploadProfileImage(
        imageFile: preparedImage,
        userId: userId,
      );
    } catch (e) {
      print('Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload cover image
  static Future<String?> uploadCoverImage({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      final preparedImage = await _prepareImageForUpload(imageFile);
      return await CloudinaryService.uploadCoverImage(
        imageFile: preparedImage,
        userId: userId,
      );
    } catch (e) {
      print('Error uploading cover image: $e');
      return null;
    }
  }

  /// Upload product images
  static Future<List<String>> uploadProductImages({
    required List<XFile> imageFiles,
    required String productId,
  }) async {
    try {
      List<dynamic> preparedImages = [];
      for (XFile imageFile in imageFiles) {
        final preparedImage = await _prepareImageForUpload(imageFile);
        preparedImages.add(preparedImage);
      }
      
      return await CloudinaryService.uploadMultipleImages(
        imageFiles: preparedImages,
        folder: 'product_images',
        basePublicId: 'product_$productId',
        quality: 85,
        width: 800,
        height: 800,
      );
    } catch (e) {
      print('Error uploading product images: $e');
      return [];
    }
  }

  /// Upload post images
  static Future<List<String>> uploadPostImages({
    required List<XFile> imageFiles,
    required String postId,
  }) async {
    try {
      print('ImageService: Preparing ${imageFiles.length} images for upload');

      List<dynamic> preparedImages = [];
      for (int i = 0; i < imageFiles.length; i++) {
        print('ImageService: Preparing image ${i + 1}/${imageFiles.length}');
        final preparedImage = await _prepareImageForUpload(imageFiles[i]);
        preparedImages.add(preparedImage);
      }

      print('ImageService: All images prepared, starting upload to Cloudinary');

      final uploadedUrls = await CloudinaryService.uploadMultipleImages(
        imageFiles: preparedImages,
        folder: 'post_images',
        basePublicId: 'post_$postId',
        quality: 80,
        width: 1080,
        height: 1080,
      );

      print('ImageService: Upload completed. ${uploadedUrls.length}/${imageFiles.length} images uploaded successfully');
      return uploadedUrls;
    } catch (e) {
      print('ImageService: Error uploading post images: $e');
      return [];
    }
  }

  /// Show image picker options dialog
  static Future<XFile?> showImagePickerDialog({
    required Function() onGalleryTap,
    required Function() onCameraTap,
  }) async {
    // This method should be called from UI with proper dialog implementation
    // For now, it just picks from gallery as default
    return await pickImageFromGallery();
  }

  /// Validate image file
  static bool validateImageFile(XFile imageFile) {
    // Check file extension
    final String extension = imageFile.path.toLowerCase().split('.').last;
    final List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
    
    if (!allowedExtensions.contains(extension)) {
      return false;
    }
    
    // Additional validations can be added here (file size, dimensions, etc.)
    return true;
  }

  /// Get image file size in MB
  static Future<double> getImageSizeInMB(XFile imageFile) async {
    if (kIsWeb) {
      final bytes = await imageFile.readAsBytes();
      return bytes.length / (1024 * 1024);
    } else {
      final file = File(imageFile.path);
      final bytes = await file.length();
      return bytes / (1024 * 1024);
    }
  }

  /// Compress image if needed
  static Future<XFile?> compressImageIfNeeded(
    XFile imageFile, {
    double maxSizeInMB = 5.0,
    int quality = 85,
  }) async {
    try {
      final currentSize = await getImageSizeInMB(imageFile);
      
      if (currentSize <= maxSizeInMB) {
        return imageFile; // No compression needed
      }
      
      // Calculate new quality based on file size
      int newQuality = (quality * (maxSizeInMB / currentSize)).round();
      newQuality = newQuality.clamp(10, 100);
      
      // Re-pick with lower quality
      // Note: This is a simplified approach. For better compression,
      // you might want to use image compression packages
      return imageFile;
    } catch (e) {
      print('Error compressing image: $e');
      return imageFile;
    }
  }

  /// Delete image from Cloudinary
  static Future<bool> deleteImage(String imageUrl) async {
    try {
      // Extract public ID from Cloudinary URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      if (pathSegments.length >= 3) {
        // Find the upload segment and get everything after it
        final uploadIndex = pathSegments.indexOf('upload');
        if (uploadIndex != -1 && uploadIndex < pathSegments.length - 1) {
          // Skip version if present (starts with 'v')
          int startIndex = uploadIndex + 1;
          if (pathSegments[startIndex].startsWith('v')) {
            startIndex++;
          }
          
          // Join remaining segments and remove file extension
          final publicIdWithExtension = pathSegments.sublist(startIndex).join('/');
          final publicId = publicIdWithExtension.split('.').first;
          
          return await CloudinaryService.deleteImage(publicId);
        }
      }
      
      return false;
    } catch (e) {
      print('Error deleting image: $e');
      return false;
    }
  }
}
