import '../models/user_model.dart';
import 'auth_service.dart';

class UserService {
  static final AuthService _authService = AuthService();

  // Get user by ID
  static Future<UserModel?> getUserById(String userId) async {
    return await _authService.getUserById(userId);
  }

  // Search users
  static Future<List<UserModel>> searchUsers({
    required String query,
    int limit = 20,
  }) async {
    return await _authService.searchUsers(query: query, limit: limit);
  }

  // Follow/Unfollow user
  static Future<void> toggleFollow({
    required String currentUserId,
    required String targetUserId,
  }) async {
    return await _authService.toggleFollowUser(
      currentUserId: currentUserId,
      targetUserId: targetUserId,
    );
  }
}
