import 'package:shared_preferences/shared_preferences.dart';

class SearchHistory {
  static const String _recentSearchesKey = 'recent_searches';
  static const int _maxRecentSearches = 10;

  // Get recent searches
  static Future<List<String>> getRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searches = prefs.getStringList(_recentSearchesKey) ?? [];
      return searches;
    } catch (e) {
      print('Error loading recent searches: $e');
      return [];
    }
  }

  // Add search to recent searches
  static Future<void> addToRecentSearches(String query) async {
    if (query.trim().isEmpty) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> searches = prefs.getStringList(_recentSearchesKey) ?? [];
      
      // Remove if already exists
      searches.remove(query);
      
      // Add to beginning
      searches.insert(0, query);
      
      // Keep only the most recent searches
      if (searches.length > _maxRecentSearches) {
        searches = searches.take(_maxRecentSearches).toList();
      }
      
      await prefs.setStringList(_recentSearchesKey, searches);
    } catch (e) {
      print('Error saving recent search: $e');
    }
  }

  // Remove specific search from recent searches
  static Future<void> removeFromRecentSearches(String query) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> searches = prefs.getStringList(_recentSearchesKey) ?? [];
      searches.remove(query);
      await prefs.setStringList(_recentSearchesKey, searches);
    } catch (e) {
      print('Error removing recent search: $e');
    }
  }

  // Clear all recent searches
  static Future<void> clearRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_recentSearchesKey);
    } catch (e) {
      print('Error clearing recent searches: $e');
    }
  }
}
